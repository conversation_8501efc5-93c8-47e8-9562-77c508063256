import { useMemo, useEffect, useRef, useState } from 'react'
import { useScrollSync } from '../../../hooks/useScrollSync'
import moment from 'moment-timezone'
import { MultiLineChart } from '../../common/MultiLineChart.jsx'
import { useResponseState } from '../../../common/useResponsiveState.js'
import { EchartsLine } from '../../common/EChartsLine.jsx'
import { Box, Typography, Tooltip, Modal } from '@mui/material'
import { getBpThresholdLevel } from '../../../thresholds/threshold/getBpThresholdLevel'
import {
  getBloodPressureColorForLevel,
  getPulseColor,
} from '../../../utils/thresholdColors.js'
import AHALogo from '../../../images/AHA-logo.svg'

// Import utilities and constants
import {
  getMaxValue,
  getMinValue,
  getXAxisInterval,
  getChartWidth,
  convertToMilliseconds,
  formatChartDate,
  filterDataByDateRange,
  getSymbolSize,
  getLabelFontSize,
} from '../../../utils/chartUtils'
import {
  SYSTOLIC_CATEGORIES,
  DIASTOLIC_CATEGORIES,
  CHART_COLORS,
  LINE_STYLES,
  TARGET_POINTS_VISIBLE,
  LABEL_CONFIGS,
} from '../../../constants/chartConstants'
import {
  tooltipContainerStyles,
  ahaLogoContainerStyles,
  ahaLogoImageStyles,
  tooltipHeaderStyles,
  tooltipHeaderTextStyles,
  categoryItemStyles,
  categoryColorDotStyles,
  categoryRangeTextStyles,
  categoryCategoryTextStyles,
  legendContainerStyles,
  legendItemContainerStyles,
  legendItemStyles,
  legendIconStyles,
  legendTextStyles,
  legendLineStyles,
} from '../../../styles/chartStyles'
import {
  chartMainContainerStyles,
  yAxisContainerStyles,
  scrollableChartContainerStyles,
  chartContentStyles,
} from '../../../styles/chartStyles'

/**
 * Systolic blood pressure tooltip component
 * @returns {JSX.Element} Tooltip component with systolic BP categories
 */
const SystolicTooltip = () => {
  if (!SYSTOLIC_CATEGORIES || !Array.isArray(SYSTOLIC_CATEGORIES)) {
    console.warn('SystolicTooltip: SYSTOLIC_CATEGORIES is not available')
    return null
  }

  return (
    <Box sx={tooltipContainerStyles}>
      {/* AHA Logo in top right corner */}
      <Box sx={ahaLogoContainerStyles}>
        <img
          src={AHALogo}
          alt="American Heart Association"
          style={ahaLogoImageStyles}
        />
      </Box>

      <Box sx={tooltipHeaderStyles}>
        <Typography variant="subtitle1" sx={tooltipHeaderTextStyles}>
          Systolic mmHg
        </Typography>
        <Typography variant="subtitle1" sx={tooltipHeaderTextStyles}>
          BP Category
        </Typography>
      </Box>
      {SYSTOLIC_CATEGORIES.map((item, index) => {
        if (!item) {
          console.warn('SystolicTooltip: Invalid category item at index', index)
          return null
        }

        return (
          <Box key={index} sx={categoryItemStyles}>
            <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              <Box
                sx={{
                  ...categoryColorDotStyles,
                  backgroundColor: item.color,
                }}
              />
              <Typography sx={categoryRangeTextStyles}>{item.range}</Typography>
            </Box>
            <Typography sx={categoryCategoryTextStyles}>
              {item.category}
            </Typography>
          </Box>
        )
      })}
    </Box>
  )
}

/**
 * Diastolic blood pressure tooltip component
 * @returns {JSX.Element} Tooltip component with diastolic BP categories
 */
const DiastolicTooltip = () => {
  if (!DIASTOLIC_CATEGORIES || !Array.isArray(DIASTOLIC_CATEGORIES)) {
    console.warn('DiastolicTooltip: DIASTOLIC_CATEGORIES is not available')
    return null
  }

  return (
    <Box sx={tooltipContainerStyles}>
      {/* AHA Logo in top right corner */}
      <Box sx={ahaLogoContainerStyles}>
        <img
          src={AHALogo}
          alt="American Heart Association"
          style={ahaLogoImageStyles}
        />
      </Box>

      <Box sx={tooltipHeaderStyles}>
        <Typography variant="subtitle1" sx={tooltipHeaderTextStyles}>
          Diastolic mmHg
        </Typography>
        <Typography variant="subtitle1" sx={tooltipHeaderTextStyles}>
          BP Category
        </Typography>
      </Box>
      {DIASTOLIC_CATEGORIES.map((item, index) => {
        if (!item) {
          console.warn(
            'DiastolicTooltip: Invalid category item at index',
            index
          )
          return null
        }

        return (
          <Box key={index} sx={categoryItemStyles}>
            <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              <Box
                sx={{
                  ...categoryColorDotStyles,
                  backgroundColor: item.color,
                }}
              />
              <Typography sx={categoryRangeTextStyles}>{item.range}</Typography>
            </Box>
            <Typography sx={categoryCategoryTextStyles}>
              {item.category}
            </Typography>
          </Box>
        )
      })}
    </Box>
  )
}

/**
 * Blood pressure legend component
 * @param {Object} props - Component props
 * @param {number} props.durationDays - Duration in days for layout adjustment
 * @param {boolean} props.isMobile - Mobile device flag
 * @returns {JSX.Element} BP legend component
 */
const BPLegend = ({ durationDays = 30, isMobile }) => {
  const [mobileTooltip, setMobileTooltip] = useState(null) // 'systolic', 'diastolic', or null

  // Add validation
  if (typeof durationDays !== 'number') {
    console.warn('BPLegend: durationDays must be a number')
    return null
  }

  const handleMobileTooltipOpen = (type) => {
    setMobileTooltip(type)
  }

  const handleMobileTooltipClose = () => {
    setMobileTooltip(null)
  }

  if (isMobile) {
    // Mobile: inline legend at bottom of chart with click tooltips
    return (
      <>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            gap: 3,
            paddingTop: 2,
            paddingBottom: 1,
            borderTop: '1px solid #f0f0f0',
            backgroundColor: '#fff',
          }}
        >
          {/* Systolic */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
            }}
            onClick={() => handleMobileTooltipOpen('systolic')}
          >
            <Box sx={legendIconStyles(CHART_COLORS.SYSTOLIC)}>!</Box>
            <Typography sx={legendTextStyles(CHART_COLORS.SYSTOLIC)}>
              Systolic
            </Typography>
          </Box>

          {/* Diastolic */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
            }}
            onClick={() => handleMobileTooltipOpen('diastolic')}
          >
            <Box sx={legendIconStyles(CHART_COLORS.DIASTOLIC)}>!</Box>
            <Typography sx={legendTextStyles(CHART_COLORS.DIASTOLIC)}>
              Diastolic
            </Typography>
          </Box>
        </Box>

        {/* Mobile Tooltip Modal */}
        <Modal
          open={!!mobileTooltip}
          onClose={handleMobileTooltipClose}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1300,
          }}
        >
          <Box
            sx={{
              backgroundColor: '#fff',
              color: '#333',
              boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
              border: '1px solid #e0e0e0',
              borderRadius: 2,
              maxWidth: 'none',
              margin: 2,
            }}
          >
            {/* Tooltip content */}
            {mobileTooltip === 'systolic' && <SystolicTooltip />}
            {mobileTooltip === 'diastolic' && <DiastolicTooltip />}
          </Box>
        </Modal>
      </>
    )
  }

  return (
    <Box sx={legendContainerStyles(durationDays, isMobile)}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        {/* Systolic */}
        <Box sx={legendItemContainerStyles}>
          <Tooltip
            title={<SystolicTooltip />}
            placement="left"
            componentsProps={{
              tooltip: {
                sx: {
                  backgroundColor: '#fff',
                  color: '#333',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                  border: '1px solid #e0e0e0',
                  borderRadius: 2,
                  maxWidth: 'none',
                },
              },
            }}
          >
            <Box sx={legendItemStyles}>
              <Box sx={legendIconStyles(CHART_COLORS.SYSTOLIC)}>!</Box>
              <Typography sx={legendTextStyles(CHART_COLORS.SYSTOLIC)}>
                Systolic
              </Typography>
            </Box>
          </Tooltip>
          <Box sx={legendLineStyles(CHART_COLORS.SYSTOLIC)} />
        </Box>

        {/* Diastolic */}
        <Box sx={legendItemContainerStyles}>
          <Tooltip
            title={<DiastolicTooltip />}
            placement="left"
            componentsProps={{
              tooltip: {
                sx: {
                  backgroundColor: '#fff',
                  color: '#333',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                  border: '1px solid #e0e0e0',
                  borderRadius: 2,
                  maxWidth: 'none',
                },
              },
            }}
          >
            <Box sx={legendItemStyles}>
              <Box sx={legendIconStyles(CHART_COLORS.DIASTOLIC)}>!</Box>
              <Typography sx={legendTextStyles(CHART_COLORS.DIASTOLIC)}>
                Diastolic
              </Typography>
            </Box>
          </Tooltip>
          <Box sx={legendLineStyles(CHART_COLORS.DIASTOLIC)} />
        </Box>
      </Box>
    </Box>
  )
}

/**
 * Heart rate legend component
 * @param {Object} props - Component props
 * @param {number} props.durationDays - Duration in days for layout adjustment
 * @param {boolean} props.isMobile - Mobile device flag
 * @returns {JSX.Element} HR legend component
 */
const HRLegend = ({ durationDays = 30, isMobile }) => {
  // Add validation
  if (typeof durationDays !== 'number') {
    console.warn('HRLegend: durationDays must be a number')
    return null
  }

  if (isMobile) {
    // Mobile: simple inline legend at bottom of chart
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          gap: 3,
          paddingTop: 2,
          paddingBottom: 1,
          borderTop: '1px solid #f0f0f0',
          backgroundColor: '#fff',
        }}
      >
        {/* Pulse */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={legendIconStyles(CHART_COLORS.PULSE)}>!</Box>
          <Typography sx={legendTextStyles(CHART_COLORS.PULSE)}>
            Pulse
          </Typography>
        </Box>
      </Box>
    )
  }

  return (
    <Box sx={legendContainerStyles(durationDays, isMobile)}>
      <Box sx={legendItemContainerStyles}>
        <Box sx={legendItemStyles}>
          <Box sx={legendIconStyles(CHART_COLORS.PULSE)}>!</Box>
          <Typography sx={legendTextStyles(CHART_COLORS.PULSE)}>
            Pulse
          </Typography>
        </Box>
        <Box sx={legendLineStyles(CHART_COLORS.PULSE)} />
      </Box>
    </Box>
  )
}

/**
 * Blood pressure chart wrapper component
 * @param {Object} props - Component props
 * @param {string} props.timeZone - Timezone string
 * @param {Object} props.threshold - Threshold configuration
 * @param {Array} props.chartData - Chart data array
 * @param {string} props.imei - Device IMEI
 * @param {string|Date} props.startDate - Start date
 * @param {string|Date} props.endDate - End date
 * @param {number} props.durationDays - Duration in days
 * @param {boolean} props.fitView - Should fit to view
 * @param {Object} props.scrollRef - Scroll reference
 * @param {Object} props.syncScrollRef - Sync scroll reference
 * @param {boolean} props.isAllTimeFilter - Is all time filter active
 * @returns {JSX.Element} Blood pressure chart component
 */
export const BloodPressureChart = ({
  timeZone,
  threshold,
  chartData = [],
  imei,
  startDate,
  endDate,
  durationDays = 30,
  fitView = false,
  scrollRef,
  syncScrollRef,
  isAllTimeFilter = false,
  isMobile = false,
  showAllData = false,
  showRecentData = true,
  mobileDataMode = 'normal',
  originalDataLength = 0,
}) => {
  // First call all hooks
  const data = useMemo(() => {
    // Add validation inside useMemo
    if (!timeZone) {
      console.warn('BloodPressureChart: timeZone is required')
      return []
    }

    if (!chartData || !Array.isArray(chartData)) {
      console.warn('BloodPressureChart: chartData must be a valid array')
      return []
    }

    try {
      return chartData
        .map((item) => {
          if (!item || !item.ts) {
            console.warn('BloodPressureChart: Invalid data item', item)
            return null
          }

          const taken = formatChartDate(item.ts, timeZone)
          return {
            date: taken,
            Systolic: parseInt(item.systolic) || 0,
            Diastolic: parseInt(item.diastolic) || 0,
          }
        })
        .filter(Boolean) // Remove null items
    } catch (error) {
      console.error('BloodPressureChart: Error processing chart data', error)
      return []
    }
  }, [chartData, timeZone])

  // Then do validation for early return
  if (!timeZone || !chartData || !Array.isArray(chartData)) {
    return null
  }

  return (
    <BloodPressureEChart
      timeZone={timeZone}
      chartData={chartData}
      imei={imei}
      startDate={startDate}
      endDate={endDate}
      durationDays={durationDays}
      threshold={threshold}
      fitView={fitView}
      scrollRef={scrollRef}
      syncScrollRef={syncScrollRef}
      isAllTimeFilter={isAllTimeFilter}
      isMobile={isMobile}
      showAllData={showAllData}
      showRecentData={showRecentData}
      mobileDataMode={mobileDataMode}
      originalDataLength={originalDataLength}
    />
  )
}

/**
 * Enhanced blood pressure chart component with ECharts
 * @param {Object} props - Component props
 * @param {string} props.timeZone - Timezone string
 * @param {Array} props.chartData - Initial chart data
 * @param {Object} props.chartOptions - Chart configuration options
 * @param {string} props.imei - Device IMEI
 * @param {boolean} props.fitView - Should fit to view
 * @param {string|Date} props.startDate - Start date
 * @param {string|Date} props.endDate - End date
 * @param {number} props.durationDays - Duration in days
 * @param {Object} props.threshold - Threshold configuration
 * @param {Object} props.scrollRef - Scroll reference
 * @param {Object} props.syncScrollRef - Sync scroll reference
 * @param {boolean} props.isAllTimeFilter - Is all time filter active
 * @returns {JSX.Element} Enhanced blood pressure chart component
 */
export const BloodPressureEChart = ({
  timeZone,
  chartData: initialChartData = [],
  chartOptions = {},
  imei,
  fitView = false,
  startDate,
  endDate,
  durationDays = 30,
  threshold,
  scrollRef,
  syncScrollRef,
  isAllTimeFilter = false,
  isMobile = false,
  showAllData = false,
  showRecentData = true,
  mobileDataMode = 'normal',
  originalDataLength = 0,
}) => {
  // Use props values instead of hook

  // Filter data based on date range using utility function
  const chartData = useMemo(() => {
    if (!timeZone) {
      console.warn('BloodPressureEChart: timeZone is required')
      return []
    }

    try {
      return filterDataByDateRange(
        initialChartData,
        startDate,
        endDate,
        isAllTimeFilter
      )
    } catch (error) {
      console.error('BloodPressureEChart: Error filtering data', error)
      return initialChartData
    }
  }, [initialChartData, startDate, endDate, timeZone, isAllTimeFilter])

  // Calculate chart width: mobile uses mobileDataMode logic, desktop uses provider logic
  const chartWidth = useMemo(() => {
    const dataPoints = chartData.length

    // Mobile logic: use mobileDataMode
    if (isMobile) {
      switch (mobileDataMode) {
        case 'recentScroll': {
          // 3-24 months: allow scrolling through all data
          if (dataPoints <= 5) {
            return '100%'
          }
          // Calculate width to show ~5 points visible with scrolling
          const scrollWidthPercentage = Math.max(200, (dataPoints / 5) * 100)
          return `${scrollWidthPercentage}%`
        }

        case 'allSqueeze':
          // All time OR Show All Data: squeeze all points to fit
          return '100%'

        default:
          return '100%'
      }
    }

    // Desktop logic: use simple provider-dashboard-like logic
    try {
      return getChartWidth(chartData.length, isAllTimeFilter, fitView)
    } catch (error) {
      console.error('BloodPressureEChart: Error calculating chart width', error)
      return '100%'
    }
  }, [chartData.length, fitView, isAllTimeFilter, isMobile, mobileDataMode])

  const { lines, dates } = useMemo(() => {
    try {
      // Process data with error handling
      const data = chartData
        .map((item) => {
          if (!item || !item.ts) {
            console.warn('BloodPressureEChart: Invalid item data', item)
            return null
          }

          const timestampMs = convertToMilliseconds(item.ts)
          if (!timestampMs) {
            console.warn('BloodPressureEChart: Invalid timestamp', item.ts)
            return null
          }

          const momentDate = moment(timestampMs)
          if (!momentDate.isValid()) {
            console.warn(
              'BloodPressureEChart: Invalid moment date',
              timestampMs
            )
            return null
          }

          const taken = formatChartDate(item.ts, timeZone)

          return {
            systolic: parseInt(item.systolic) || 0,
            diastolic: parseInt(item.diastolic) || 0,
            date: taken,
            timestamp: momentDate.valueOf(),
          }
        })
        .filter(Boolean)

      // Sort data by timestamp (oldest to newest for correct X-axis display)
      data.sort((a, b) => a.timestamp - b.timestamp)

      // Get symbol and label sizes using utility functions
      const symbolSize = getSymbolSize(isAllTimeFilter, data.length)
      const labelFontSize = getLabelFontSize(isAllTimeFilter, data.length)

      // Create systolic line configuration
      const lines = [
        {
          name: 'Sys',
          data: data.map((item) => item.systolic),
          type: 'line',
          smooth: 0.2,
          symbol: 'circle',
          symbolSize: symbolSize,
          showSymbol: true,
          label: {
            ...LABEL_CONFIGS.DEFAULT,
            fontSize: labelFontSize,
          },
          itemStyle: {
            color: (params) => {
              try {
                const dataIndex = params.dataIndex
                const systolic = data[dataIndex]?.systolic
                const diastolic = data[dataIndex]?.diastolic
                const bpThresholds = threshold?.bloodPressure || threshold

                if (!bpThresholds) {
                  return CHART_COLORS.SYSTOLIC
                }

                const systolicLevel = getBpThresholdLevel(
                  systolic,
                  null,
                  bpThresholds
                )

                return getBloodPressureColorForLevel(
                  systolicLevel,
                  systolic,
                  diastolic,
                  bpThresholds
                )
              } catch (error) {
                console.warn(
                  'BloodPressureEChart: Error calculating systolic color',
                  error
                )
                return CHART_COLORS.SYSTOLIC
              }
            },
          },
          lineStyle: {
            ...LINE_STYLES.DEFAULT,
            color: CHART_COLORS.SYSTOLIC,
          },
          dot: ({ cx, cy, value, stroke, r, strokeWidth }) => {
            if (cx === undefined || cy === undefined || value === undefined) {
              return null
            }

            try {
              const dataIndex = data.findIndex(
                (item) => item.systolic === value
              )
              const systolic = data[dataIndex]?.systolic
              const diastolic = data[dataIndex]?.diastolic
              const bpThresholds = threshold?.bloodPressure || threshold

              const systolicLevel = getBpThresholdLevel(
                systolic,
                null,
                bpThresholds
              )

              const color =
                getBloodPressureColorForLevel(
                  systolicLevel,
                  systolic,
                  diastolic,
                  bpThresholds
                ) || CHART_COLORS.SYSTOLIC

              return (
                <circle
                  cx={cx}
                  cy={cy}
                  r={r}
                  stroke={color}
                  fill={color}
                  strokeWidth={strokeWidth}
                />
              )
            } catch (error) {
              console.warn(
                'BloodPressureEChart: Error rendering systolic dot',
                error
              )
              return null
            }
          },
        },
        {
          name: 'Dias',
          data: data.map((item) => item.diastolic),
          type: 'line',
          smooth: 0.2,
          symbol: 'circle',
          symbolSize: symbolSize,
          showSymbol: true,
          label: {
            show: true,
            position: 'bottom',
            fontSize: labelFontSize,
            color: '#000000',
            formatter: '{c}',
            distance: 1,
            align: 'center',
            fontWeight: 'bold',
            padding: [-30, 0, 0, 0],
          },
          itemStyle: {
            color: (params) => {
              const dataIndex = params.dataIndex
              const systolic = data[dataIndex]?.systolic
              const diastolic = data[dataIndex]?.diastolic
              const bpThresholds = threshold?.bloodPressure || threshold
              const systolicLevel = getBpThresholdLevel(
                systolic,
                null,
                bpThresholds
              )
              const diastolicLevel = getBpThresholdLevel(
                null,
                diastolic,
                bpThresholds
              )
              return getBloodPressureColorForLevel(
                diastolicLevel,
                systolic,
                diastolic,
                bpThresholds
              )
            },
          },
          lineStyle: {
            width: 2,
            color: '#4CAF50',
            type: 'dashed',
            dashArray: [4, 4],
          },
          dot: ({ cx, cy, value, stroke, r, strokeWidth }) => {
            if (cx === undefined || cy === undefined || value === undefined)
              return null
            const dataIndex = data.findIndex((item) => item.diastolic === value)
            const systolic = data[dataIndex]?.systolic
            const diastolic = data[dataIndex]?.diastolic
            const bpThresholds = threshold?.bloodPressure || threshold
            const diastolicLevel = getBpThresholdLevel(
              null,
              diastolic,
              bpThresholds
            )
            const color = getBloodPressureColorForLevel(
              diastolicLevel,
              systolic,
              diastolic,
              bpThresholds
            )
            return (
              <circle
                cx={cx}
                cy={cy}
                r={r}
                stroke={color}
                fill={color}
                strokeWidth={strokeWidth}
              />
            )
          },
        },
      ]

      const dates = data.map((item) => item.date)
      return { lines, dates }
    } catch (error) {
      console.error('Chart: Error creating chart lines', error)
      return { lines: [], dates: [] }
    }
  }, [chartData, timeZone, threshold, isAllTimeFilter])

  const getXAxisInterval = (datesLength) => {
    // Mobile logic for Show All Data - avoid overlapping labels
    if (isMobile && mobileDataMode === 'allSqueeze') {
      if (datesLength <= 5) return 0 // Show all labels for 5 or fewer points
      if (datesLength <= 10) return 1 // Show every 2nd label
      if (datesLength <= 20) return Math.floor(datesLength / 5) // Show ~5 labels
      return Math.floor(datesLength / 4) // Show ~4 labels for all other cases
    }

    if (isMobile && mobileDataMode === 'recentScroll') {
      return 0
    }

    // Desktop logic (unchanged)
    const mobileFactor = isMobile ? 2 : 1

    if (durationDays === 93 || durationDays === 186) {
      return 0 // Show date under each point for 3/6 months
    }

    if (durationDays === 365 || durationDays === 730) {
      return 1 // Show date every other point for 1/2 years
    }

    if (isAllTimeFilter) {
      if (datesLength <= 10) return 0
      if (datesLength <= 30) return Math.max(2, 4 * mobileFactor)
      if (datesLength <= 100)
        return Math.floor(datesLength / (isMobile ? 8 : 15))
      if (datesLength <= 365)
        return Math.floor(datesLength / (isMobile ? 10 : 20))
      return Math.floor(datesLength / (isMobile ? 15 : 25))
    }

    const targetPointsVisible = 21
    const interval = Math.max(
      0,
      Math.floor((datesLength - 1) / (targetPointsVisible - 1))
    )
    return Math.max(interval, interval * mobileFactor)
  }

  const getRightMargin = () => {
    if (durationDays <= 31) {
      return '3%'
    } else if (durationDays <= 93) {
      return '2%'
    } else {
      return '1%'
    }
  }

  const defaultOptions = {
    grid: {
      top: '10%',
      right: getRightMargin(),
      bottom: '5%',
      left: '1%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#999',
          width: 2,
        },
      },
      formatter: function (params) {
        const systolic = params[0]?.data
        const diastolic = params[1]?.data
        const date = params[0]?.axisValueLabel

        const bpThresholds = threshold?.bloodPressure || threshold
        const systolicLevel = getBpThresholdLevel(systolic, null, bpThresholds)
        const diastolicLevel = getBpThresholdLevel(
          null,
          diastolic,
          bpThresholds
        )

        return `
    <div style="white-space: nowrap; color: #333; font-size: 14px;">
      <strong>${date}</strong><br />
      <div style="display: flex; align-items: center; margin-top: 4px;">
        <div style="width: 10px; height: 10px; background-color: ${getBloodPressureColorForLevel(systolicLevel, systolic, diastolic, bpThresholds)}; border-radius: 50%; margin-right: 8px;"></div>
        <span>Sys: ${systolic}</span>
      </div>
      <div style="display: flex; align-items: center; margin-top: 4px;">
        <div style="width: 10px; height: 10px; background-color: ${getBloodPressureColorForLevel(diastolicLevel, systolic, diastolic, bpThresholds)}; border-radius: 50%; margin-right: 8px;"></div>
        <span>Dias: ${diastolic}</span>
      </div>
    </div>
        `
      },
    },
    xAxis: {
      type: 'category',
      data: dates,
      boundaryGap: false,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E0E0E0',
          width: 0.5,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#9E9E9E',
        margin: 10,
        interval: getXAxisInterval(dates.length),
        rich: {
          date: {
            fontWeight: 'lighter',
            fontSize: isMobile ? 10 : 9,
            color: '#000000',
            lineHeight: isMobile ? 12 : 12,
          },
          time: {
            fontWeight: 'normal',
            fontSize: isMobile ? 10 : 10,
            color: '#9E9E9E',
            lineHeight: isMobile ? 12 : 14,
          },
        },
        formatter: function (value) {
          const [date, time] = value.split('\n')
          if (date && time) {
            return '{date|' + date + '}\n{time|' + time + '}'
          } else if (date) {
            return '{date|' + date + '}'
          } else {
            return value
          }
        },
      },
    },
    yAxis: {
      show: true,
      type: 'value',
      min: getMinValue(chartData, 'diastolic'),

      max: getMaxValue(chartData, 'systolic'),
      splitLine: {
        show: false,
      },
      axisLabel: {
        margin: 30,
      },
    },
    series: lines,

    legend: {
      show: false,
    },
  }

  const options = {
    ...defaultOptions,
    ...chartOptions,
  }

  const yMin = getMinValue(chartData, 'diastolic') - 10
  const yMax = getMaxValue(chartData, 'systolic') + 10

  const yAxisOnlyOption = {
    grid: {
      left: 0,
      right: 0,
      top: 25,
      bottom: 45,
      containLabel: true,
    },
    xAxis: { show: false },
    yAxis: {
      show: true,
      type: 'value',
      min: yMin,
      max: yMax,
      splitLine: { show: false },
      axisLabel: { margin: 30 },
    },
    series: [],
    animation: false,
    tooltip: { show: false },
    legend: { show: false },
  }

  const mainChartOption = {
    ...defaultOptions,
    yAxis: {
      show: false,
      type: 'value',
      min: yMin,
      max: yMax,
    },
    grid: {
      ...defaultOptions.grid,
      left: 0,
    },
  }

  // Use optimized scroll sync hook (disabled - handled externally)
  // useScrollSync(scrollRef, syncScrollRef, {
  //   enabled: Boolean(scrollRef?.current && syncScrollRef?.current),
  // })

  if (isMobile) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            flex: 1,
            height: 'calc(100% - 40px)',
          }}
        >
          <Box
            sx={{
              width: 60,
              minWidth: 60,
              maxWidth: 60,
              height: '100%',
              position: 'sticky',
              zIndex: 2,
              background: '#fff',
            }}
          >
            <EchartsLine option={yAxisOnlyOption} />
          </Box>
          <Box
            sx={{
              flex: 1,
              height: '100%',
              overflowX: mobileDataMode === 'recentScroll' ? 'auto' : 'hidden',
              overflowY: 'hidden',
              direction: 'rtl', // Start scroll from right (newest data)
              '&::-webkit-scrollbar': {
                height: '8px',
                display: 'none',
              },
            }}
            ref={scrollRef}
          >
            <Box sx={{ width: chartWidth, height: '100%', direction: 'ltr' }}>
              <EchartsLine option={mainChartOption} />
            </Box>
          </Box>
        </Box>
        <BPLegend durationDays={durationDays} isMobile={isMobile} />
      </Box>
    )
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Box
        sx={{
          width: 60,
          minWidth: 60,
          maxWidth: 60,
          height: '100%',
          position: 'sticky',
          zIndex: 2,
          background: '#fff',
        }}
      >
        <EchartsLine option={yAxisOnlyOption} />
      </Box>
      <Box
        sx={{
          flex: 1,
          height: '100%',
          overflowX: (() => {
            // Desktop logic
            if (isAllTimeFilter || chartData.length <= 19) {
              return 'hidden'
            }
            return 'auto'
          })(),
          overflowY: 'hidden',
          direction: 'rtl', // Start scroll from right (newest data)
          '&::-webkit-scrollbar': {
            height: '8px',
            display: (() => {
              // Desktop logic
              if (isAllTimeFilter || chartData.length <= 19) {
                return 'none'
              }
              return 'block'
            })(),
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#E0E0E0',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: '#999',
            },
          },
        }}
        ref={scrollRef}
      >
        <Box sx={{ width: chartWidth, height: '100%', direction: 'ltr' }}>
          <EchartsLine option={mainChartOption} />
        </Box>
      </Box>
      <BPLegend durationDays={durationDays} isMobile={isMobile} />
    </Box>
  )
}

export const HeartRateChart = ({
  timeZone,
  threshold,
  chartData: initialChartData = [],
  startDate,
  endDate,
  durationDays,
  fitView = false,
  scrollRef,
  syncScrollRef,
  isAllTimeFilter = false,
  isMobile = false,
  showAllData = false,
  showRecentData = true,
  mobileDataMode = 'normal',
  originalDataLength = 0,
}) => {
  // Use props values instead of hook

  // Filter data based on date range
  const chartData = useMemo(() => {
    return filterDataByDateRange(
      initialChartData,
      startDate,
      endDate,
      isAllTimeFilter
    )
  }, [initialChartData, startDate, endDate, isAllTimeFilter])

  return (
    <HeartRateEChart
      timeZone={timeZone}
      chartData={chartData}
      threshold={threshold}
      startDate={startDate}
      endDate={endDate}
      durationDays={durationDays}
      fitView={fitView}
      scrollRef={scrollRef}
      syncScrollRef={syncScrollRef}
      isAllTimeFilter={isAllTimeFilter}
      isMobile={isMobile}
      showAllData={showAllData}
      showRecentData={showRecentData}
      mobileDataMode={mobileDataMode}
      originalDataLength={originalDataLength}
    />
  )
}

export const HeartRateEChart = ({
  timeZone,
  chartData: initialChartData = [],
  chartOptions = {},
  imei,
  fitView = false,
  startDate,
  endDate,
  durationDays,
  threshold,
  scrollRef,
  syncScrollRef,
  isAllTimeFilter = false,
  isMobile = false,
  showAllData = false,
  showRecentData = true,
  mobileDataMode = 'normal',
  originalDataLength = 0,
}) => {
  // Use props values instead of hook

  // Filter data based on date range
  const chartData = useMemo(() => {
    return filterDataByDateRange(
      initialChartData,
      startDate,
      endDate,
      isAllTimeFilter
    )
  }, [initialChartData, startDate, endDate, isAllTimeFilter])

  // Calculate chart width: mobile uses mobileDataMode logic, desktop uses provider logic
  const chartWidth = useMemo(() => {
    const dataPoints = chartData.length

    // Mobile logic: use mobileDataMode
    if (isMobile) {
      switch (mobileDataMode) {
        case 'recentScroll': {
          // 3-24 months: allow scrolling through all data
          if (dataPoints <= 5) {
            return '100%'
          }
          // Calculate width to show ~5 points visible with scrolling
          const scrollWidthPercentage = Math.max(200, (dataPoints / 5) * 100)
          return `${scrollWidthPercentage}%`
        }

        case 'allSqueeze':
          // All time OR Show All Data: squeeze all points to fit
          return '100%'

        default:
          return '100%'
      }
    }

    // Desktop logic: use simple provider-dashboard-like logic
    if (isAllTimeFilter) {
      return '100%'
    }

    if (dataPoints <= 21) {
      return '100%'
    }

    const targetPointsVisible = 21
    const widthPercentage = Math.max(
      100,
      (dataPoints / targetPointsVisible) * 100
    )
    return fitView ? '100%' : `${widthPercentage}%`
  }, [chartData.length, fitView, isAllTimeFilter, isMobile, mobileDataMode])

  const { lines, dates } = useMemo(() => {
    try {
      const years = new Set()
      chartData.forEach((item) => {
        if (item && item.ts) {
          const timestampMs =
            typeof item.ts === 'number' && item.ts < 10000000000
              ? item.ts * 1000
              : item.ts
          const year = moment(timestampMs).year()
          years.add(year)
        }
      })
      const hasMultipleYears = years.size > 1

      const data = chartData
        .filter((item) => item && item.ts != null) // Filter out items with null/undefined timestamps
        .map((item) => {
          let taken = ''
          const timestampMs =
            typeof item.ts === 'number' && item.ts < 10000000000
              ? item.ts * 1000
              : item.ts

          const momentDate = moment(timestampMs)

          if (momentDate.isValid()) {
            const dateFormat = 'MM/DD/YY'
            taken =
              momentDate.tz(timeZone).format(dateFormat) +
              '\n' +
              momentDate.tz(timeZone).format('hh:mmA')
          } else {
            console.warn('Invalid date:', item.ts)
            return null // Return null for invalid dates
          }

          return {
            pulse: item.pulse,
            date: taken,
            timestamp: momentDate.valueOf(),
          }
        })
        .filter(Boolean) // Remove null entries

      // Sort data by timestamp (oldest to newest for correct X-axis display)
      data.sort((a, b) => a.timestamp - b.timestamp)
      const symbolSize = isAllTimeFilter && data.length > 50 ? 6 : 10
      const labelFontSize = isAllTimeFilter && data.length > 50 ? 10 : 12

      const lines = [
        {
          name: 'Pulse',
          data: data.map((item) => item.pulse),
          type: 'line',
          smooth: 0.2,
          symbol: 'circle',
          symbolSize: symbolSize,
          showSymbol: true,
          label: {
            show: true,
            position: 'top',
            fontSize: labelFontSize,
            color: '#000000',
            formatter: '{c}',
            distance: 1,
            align: 'center',
            fontWeight: 'bold',
            padding: [0, 0, 10, 0],
          },
          itemStyle: {
            color: (params) => {
              const pulseValue = params.value
              const pulseLevel = getBpThresholdLevel(
                null,
                null,
                threshold,
                pulseValue
              )
              return getPulseColor(pulseValue, threshold)
            },
          },
          lineStyle: {
            width: 2,
            color: '#3F51B5',
          },
          dot: ({ cx, cy, value, stroke, r, strokeWidth }) => {
            if (cx === undefined || cy === undefined || value === undefined)
              return null
            const pulseLevel = getBpThresholdLevel(null, null, threshold, value)
            const color = getPulseColor(value, threshold)
            return (
              <circle
                cx={cx}
                cy={cy}
                r={r}
                stroke={color}
                fill={color}
                strokeWidth={strokeWidth}
              />
            )
          },
        },
      ]

      const dates = data.map((item) => item.date)
      return { lines, dates }
    } catch (error) {
      console.error('Chart: Error creating chart lines', error)
      return { lines: [], dates: [] }
    }
  }, [chartData, timeZone, threshold, isAllTimeFilter])

  const getXAxisInterval = (datesLength) => {
    // Mobile logic for Show All Data - avoid overlapping labels
    if (isMobile && mobileDataMode === 'allSqueeze') {
      if (datesLength <= 5) return 0 // Show all labels for 5 or fewer points
      if (datesLength <= 10) return 1 // Show every 2nd label
      if (datesLength <= 20) return Math.floor(datesLength / 5) // Show ~5 labels
      return Math.floor(datesLength / 4) // Show ~4 labels for all other cases
    }

    // Mobile logic for recentScroll - show labels under every point
    if (isMobile && mobileDataMode === 'recentScroll') {
      return 0 // ✅ Show date under every point for mobile
    }

    // Desktop logic (unchanged)
    const mobileFactor = isMobile ? 2 : 1

    if (durationDays === 93 || durationDays === 186) {
      return 0 // Show date under each point for 3/6 months
    }

    if (durationDays === 365 || durationDays === 730) {
      return 1 // Show date every other point for 1/2 years
    }

    if (isAllTimeFilter) {
      if (datesLength <= 10) return 0
      if (datesLength <= 30) return Math.max(2, 4 * mobileFactor)
      if (datesLength <= 100)
        return Math.floor(datesLength / (isMobile ? 8 : 15))
      if (datesLength <= 365)
        return Math.floor(datesLength / (isMobile ? 10 : 20))
      return Math.floor(datesLength / (isMobile ? 15 : 25))
    }

    const targetPointsVisible = 21
    const interval = Math.max(
      0,
      Math.floor((datesLength - 1) / (targetPointsVisible - 1))
    )
    return Math.max(interval, interval * mobileFactor)
  }

  const getRightMarginHR = () => {
    if (durationDays <= 31) {
      return '8%'
    } else if (durationDays <= 93) {
      return '4%'
    } else {
      return '1%'
    }
  }

  const defaultOptions = {
    grid: {
      top: '10%',
      right: getRightMarginHR(),
      bottom: '5%',
      left: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#999',
          width: 2,
        },
      },
      formatter: function (params) {
        const pulse = params[0]?.data
        const date = params[0]?.axisValueLabel
        const time = chartData[params[0]?.dataIndex]?.time
        const color = getPulseColor(pulse, threshold)

        return `
    <div style="white-space: nowrap; color: #333; font-size: 14px;">
      <strong>${date}</strong><br />
      <div style="display: flex; align-items: center; margin-top: 4px;">
        <div style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></div>
        <span>Pulse: ${pulse}</span>
      </div>
    </div>
        `
      },
    },
    xAxis: {
      type: 'category',
      data: dates,
      boundaryGap: false,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E0E0E0',
          width: 0.5,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        margin: 10,
        interval: getXAxisInterval(dates.length),
        rich: {
          date: {
            fontWeight: 'lighter',
            fontSize: isMobile ? 10 : 9,
            color: '#000000',
            lineHeight: isMobile ? 12 : 12,
          },
          time: {
            fontWeight: 'normal',
            fontSize: isMobile ? 10 : 10,
            color: '#9E9E9E',
            lineHeight: isMobile ? 12 : 14,
          },
        },
        formatter: function (value) {
          const [date, time] = value.split('\n')
          if (date && time) {
            return '{date|' + date + '}\n{time|' + time + '}'
          } else if (date) {
            return '{date|' + date + '}'
          } else {
            return value
          }
        },
      },
    },
    yAxis: {
      show: false,
      type: 'value',
      min: 50,
      max: Math.max(getMaxValue(chartData, 'pulse'), 100),
    },
    series: lines,

    legend: {
      show: false,
    },
  }

  const options = {
    ...defaultOptions,
    ...chartOptions,
  }
  const yMinPulse = getMinValue(chartData, 'pulse') - 10
  const yMaxPulse = getMaxValue(chartData, 'pulse') + 10

  const yAxisOnlyOption = {
    grid: {
      left: 0,
      right: 0,
      top: 30,
      bottom: 40,
      containLabel: true,
    },
    xAxis: { show: false },
    yAxis: {
      show: true,
      type: 'value',
      min: yMinPulse,
      max: yMaxPulse,
      splitLine: { show: false },
      axisLabel: {
        margin: 30,
        fontSize: 12,
        color: '#9E9E9E',
      },
    },
    series: [],
  }

  const mainChartOption = {
    ...defaultOptions,
    yAxis: {
      show: false,
      type: 'value',
      min: yMinPulse,
      max: yMaxPulse,
    },
    grid: {
      ...defaultOptions.grid,
      left: 0,
      top: '40px',
    },
    series: lines.map((line) => ({
      ...line,
      label: {
        ...line.label,
        fontWeight: 600,
      },
    })),
  }

  // Use optimized scroll sync hook (disabled - handled externally)
  // useScrollSync(scrollRef, syncScrollRef, {
  //   enabled: Boolean(scrollRef?.current && syncScrollRef?.current),
  // })

  if (isMobile) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            flex: 1,
            height: 'calc(100% - 40px)',
          }}
        >
          <Box
            sx={{
              width: 60,
              minWidth: 60,
              maxWidth: 60,
              height: '100%',
              position: 'sticky',
              left: 0,
              zIndex: 2,
              background: '#fff',
            }}
          >
            <EchartsLine option={yAxisOnlyOption} />
          </Box>
          <Box
            sx={{
              flex: 1,
              height: '100%',
              overflowX: mobileDataMode === 'recentScroll' ? 'auto' : 'hidden',
              overflowY: 'hidden',
              direction: 'rtl', // Start scroll from right (newest data)
              '&::-webkit-scrollbar': {
                height: '8px',
                display: 'none',
              },
            }}
            ref={scrollRef}
          >
            <Box sx={{ width: chartWidth, height: '100%', direction: 'ltr' }}>
              <EchartsLine option={mainChartOption} />
            </Box>
          </Box>
        </Box>
        <HRLegend durationDays={durationDays} isMobile={isMobile} />
      </Box>
    )
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Box
        sx={{
          width: 60,
          minWidth: 60,
          maxWidth: 60,
          height: '100%',
          position: 'sticky',
          left: 0,
          zIndex: 2,
          background: '#fff',
        }}
      >
        <EchartsLine option={yAxisOnlyOption} />
      </Box>
      <Box
        sx={{
          flex: 1,
          height: '100%',
          overflowX: (() => {
            // Desktop logic
            if (isAllTimeFilter || chartData.length <= 19) {
              return 'hidden'
            }
            return 'auto'
          })(),
          overflowY: 'hidden',
          direction: 'rtl', // Start scroll from right (newest data)
          '&::-webkit-scrollbar': {
            height: '8px',
            display: (() => {
              // Desktop logic
              if (isAllTimeFilter || chartData.length <= 19) {
                return 'none'
              }
              return 'block'
            })(),
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#E0E0E0',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: '#999',
            },
          },
        }}
        ref={scrollRef}
      >
        <Box sx={{ width: chartWidth, height: '100%', direction: 'ltr' }}>
          <EchartsLine option={mainChartOption} />
        </Box>
      </Box>
      <HRLegend durationDays={durationDays} isMobile={isMobile} />
    </Box>
  )
}

export const BloodPressureCharts = ({
  timeZone,
  chartData: initialChartData = [],
  chartOptions = {},
  imei,
  fitView = false,
  startDate,
  endDate,
  durationDays,
  threshold,
  isAllTimeFilter = false,
}) => {
  const bpChartRef = useRef(null)
  const hrChartRef = useRef(null)

  // Filter data based on date range
  const chartData = useMemo(() => {
    return filterDataByDateRange(
      initialChartData,
      startDate,
      endDate,
      isAllTimeFilter
    )
  }, [initialChartData, startDate, endDate, isAllTimeFilter])

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
      }}
    >
      <Box sx={{ width: '100%', height: '50%' }}>
        <BloodPressureChart
          timeZone={timeZone}
          chartData={chartData}
          chartOptions={chartOptions}
          imei={imei}
          fitView={fitView}
          startDate={startDate}
          endDate={endDate}
          durationDays={durationDays}
          threshold={threshold}
          scrollRef={bpChartRef}
          syncScrollRef={hrChartRef}
          isAllTimeFilter={isAllTimeFilter}
        />
      </Box>
      <Box sx={{ width: '100%', height: '50%' }}>
        <HeartRateChart
          timeZone={timeZone}
          chartData={chartData}
          chartOptions={chartOptions}
          imei={imei}
          fitView={fitView}
          startDate={startDate}
          endDate={endDate}
          durationDays={durationDays}
          threshold={threshold}
          scrollRef={hrChartRef}
          syncScrollRef={bpChartRef}
          isAllTimeFilter={isAllTimeFilter}
        />
      </Box>
    </Box>
  )
}
