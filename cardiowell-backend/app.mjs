import express from "express"
import "dotenv/config"
import errorhandler from "errorhandler"
import logger from "morgan"
import responseTime from "response-time"
import bodyParser from "body-parser"
import cors from "cors"
import mongoose from "mongoose"
import session from "express-session"
import MongoStore from "connect-mongo"
import { CronJob } from "cron"
import passport from "passport"
import path from "path"
import http from "http"
import io from "socket.io"
import Provider from "./models/provider.js"
import SuperAdmin from "./models/superAdmin.js"
import Clinic from "./models/clinic.js"
import { customerRouter } from "./routes/customer.mjs"
import { providerRouter } from "./routes/provider.mjs"
import { deviceRouter } from "./routes/device.mjs"
import glucoseRouter from "./routes/iglucose.js"
import transtekRouter from "./routes/transtek.mjs"
import bodyTraceRouter from "./routes/bodyTrace.mjs"
import berryRouter from "./routes/berry.mjs"
import { withingsRouter } from "./routes/withings.mjs"
import { getGlucoseData } from "./controller/Iglucose.js"
import { deviceUpdatesRouter } from "./routes/deviceUpdates.mjs"
import { forwardTestRouter } from "./routes/forwardTest.mjs"
import { thresholdRouter } from "./routes/threshold.mjs"
import { programRouter } from "./routes/program.mjs"
import { userRouter } from "./routes/users.mjs"
import { profileTimeRouter } from "./routes/profileTime.mjs"
import { clinicalNoteRouter } from "./routes/clinicalNote.mjs"
import { adRouter } from "./routes/manufacturer/ad.mjs"
import {
  getPatientsForClinic,
  getPatientDataById,
  getPatientOverviewsForClinc,
} from "./users/service/patientData.mjs"
import routes from "./routes/index.js"
import { handlerTestDevicesJobStart } from "./device/service/test/handlerTestDevicesJobStart.mjs"
import { deleteOldPatientTimes } from "./profileTime/service/deleteOldPatientTimes.mjs"
import { shortUrlsRouter } from "./routes/shortUrls.mjs"
import { fileURLToPath } from "url"
import { testPatientChatRouter } from "./patient-assistant/test-patient-chat-routes.mjs"
import { twilioSmsWebhookRouter } from "./patient-assistant/twilio-sms-webhook-routes.mjs"
import { Sentry } from "./observability/sentry.mjs"
import { init } from "./models/init.mjs"
import { patientChatRouter } from "./patient-assistant/patient-chat-routes.mjs"
import {
  watchdogMiddleware,
  slowRequestLogger,
  timedDbQuery,
} from "./utils/apiTiming.mjs"
import { patientSettingsRouter } from "./patient/patient-settings-routes.mjs"

await mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
mongoose.Promise = global.Promise
const db = mongoose.connection

await init()

db.on("error", console.error.bind(console, "MongoDB connection error"))

logger.token("body", req => {
  return JSON.stringify(req.body)
})

let app = express()

app.set("trust proxy", 1)

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
app.use(express.static(path.join(__dirname, "/build")))

app.use(express.static("./client"))
app.use(
  bodyParser.json({
    verify: (req, res, buf) => {
      req.rawBody = buf.toString("utf8")
    },
  }),
)
app.use(
  bodyParser.urlencoded({
    extended: true,
    verify: (req, res, buf) => {
      req.rawBody = buf.toString("utf8")
    },
  }),
)

// Log total request time with color coding
app.use(
  responseTime((req, res, time) => {
    const colors = {
      reset: "\x1b[0m",
      red: "\x1b[31m",
      yellow: "\x1b[33m",
      green: "\x1b[32m",
      cyan: "\x1b[36m",
    }

    let timeColor = colors.green
    if (time > 5000) {
      timeColor = colors.red
    } else if (time > 2000) {
      timeColor = colors.yellow
    } else if (time > 1000) {
      timeColor = colors.cyan
    }

    const formattedTime = `${timeColor}${time.toFixed(2)}ms${colors.reset}`
    console.log(`[REQ] ${req.method} ${req.originalUrl} - ${formattedTime}`)
  }),
)

app.use(logger(":method :url :body"))

app.use(cors())

// Watchdog middleware to catch potential H12 timeouts
app.use(watchdogMiddleware())

// Slow request logger for requests that complete but are slow
app.use(slowRequestLogger(3000)) // Log requests taking >3 seconds

app.use(
  session({
    secret: "fraggle-rock", //pick a random string to make the hash that is generated secure
    store: MongoStore.create({ client: db.getClient() }),
    resave: false, //required
    saveUninitialized: false, //required
  }),
)

app.use(passport.initialize())
app.use(passport.session())

app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Credentials", true)
  res.header("Access-Control-Allow-Origin", req.headers.origin)
  res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE")
  res.header(
    "Access-Control-Allow-Headers",
    "X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept",
  )
  if ("OPTIONS" == req.method) {
    res.send(200)
  } else {
    next()
  }
})

function requireHTTPS(req, res, next) {
  if (
    !req.secure &&
    req.get("x-forwarded-proto") !== "https" &&
    process.env.NODE_ENV !== "development"
  ) {
    return res.redirect("https://" + req.get("host") + req.url)
  }
  next()
}

app.use(requireHTTPS)

app.use("/routes/customers", customerRouter)
app.use("/routes/providers", providerRouter)
app.use("/routes/devices", deviceRouter)
app.use("/routes", routes)
app.use("/routes/users", userRouter)
app.use("/routes/", userRouter)
app.use("/routes/ad", adRouter)
app.use("/routes/iglucose", glucoseRouter)
app.use("/routes/transtek", transtekRouter)
app.use("/routes/bodyTrace", bodyTraceRouter)
app.use("/routes/berry", berryRouter)
app.use("/routes/withings", withingsRouter)
app.use("/routes/device-updates", deviceUpdatesRouter)
app.use("/routes/forwardtest", forwardTestRouter)
app.use("/routes/threshold", thresholdRouter)
app.use("/routes/program", programRouter)
app.use("/routes/profiletime", profileTimeRouter)
app.use("/routes/short-urls", shortUrlsRouter)
app.use("/routes/note", clinicalNoteRouter)
app.use("/routes/chat", patientChatRouter)
app.use("/routes/patient", patientSettingsRouter)

app.use("/routes/twilio", twilioSmsWebhookRouter)
if (process.env.NODE_ENV === "development") {
  app.use("/routes/messages", testPatientChatRouter)
}

app.get("/ping", function (req, res) {
  return res.send("pong")
})

app.get("/*", function (req, res) {
  res.sendFile(path.join(import.meta.dirname, "/build", "index.html"))
})

Sentry.setupExpressErrorHandler(app)
if (process.env.NODE_ENV === "development") {
  app.use(errorhandler())
}

passport.serializeUser(function (provider, done) {
  done(null, provider.id)
})

passport.deserializeUser(function (id, done) {
  Provider.findById(id, function (err, provider) {
    if (err) {
      done(err)
    }
    if (provider) {
      done(null, provider)
    } else {
      SuperAdmin.findById(id, function (err, admin) {
        if (err) done(err)
        done(null, admin)
      })
    }
  })
})
function isLoggedIn(req, res, next) {
  if (req.isAuthenticated()) {
    return next()
  }
  return res.status(500).send({ message: "Error" })
}

const job = new CronJob("*/5 * * * *", async function () {
  // run cronJon in every 5 min to save the latest data in db
  console.log("You will see this message every second")
  try {
    await getGlucoseData()
  } catch (err) {
    console.log("ERR-------------------", err)
  }
})
job.start()

const testDevicesJob = new CronJob("*/1 * * * *", async function () {
  try {
    await handlerTestDevicesJobStart()
  } catch (err) {
    console.error(err)
  }
})
testDevicesJob.start()

const deleteOldTimerData = new CronJob("0 0 1 * *", async function () {
  try {
    await deleteOldPatientTimes()
  } catch (err) {
    console.error(err)
  }
})
deleteOldTimerData.start()

/*
  const updateBodyTraceDevicesJob = new CronJob("0 * * * *", async function () {
    try {
      await updateBodyTraceDevicesBloodPressureStatus();
    } catch (err) {
      console.error(err);
    }
  });
  updateBodyTraceDevicesJob.start();
*/

const httpServer = http.Server(app)
const ioServer = io(httpServer, {
  transports: ["websocket"],
  allowUpgrades: false,
  pingInterval: 25000,
  pingTimeout: 60000,
})
app.set("socketio", ioServer)
ioServer.on("connection", function (socket) {
  socket.on("disconnect", function () {
    console.log("User Disconnected")
  })

  // Set up interval for consistent patient data updates
  socket.on("patientData", function (emittedData) {
    Provider.findOne({ _id: emittedData.providerID }, function (err, result) {
      if (result) {
        setInterval(async function () {
          const patientArray = await getPatientOverviewsForClinc(
            emittedData.clinic,
            emittedData.providerID,
          )
          socket.emit("patientData", patientArray)
        }, 10000)
      } else {
        socket.emit("patientData", "Error: Not Authenticated")
      }
    })
  })

  // One time patient data update
  socket.on("patientDataUpdate", emittedData => {
    Provider.findOne({ _id: emittedData.providerID }, async (err, result) => {
      if (result) {
        const patientArray = await getPatientOverviewsForClinc(
          emittedData.clinic,
          emittedData.providerID,
        )
        socket.emit("patientDataUpdate", patientArray)
      } else {
        socket.emit("patientDataUpdate", "Error: Not Authenticated")
      }
    })
  })

  socket.on("patientDashboardData", async function (emittedData) {
    try {
      const patient = await getPatientDataById(emittedData.id)
      socket.emit("patientDashboardData", patient.patientObj)
    } catch (err) {
      console.error(err)
    }
  })

  socket.on("iFrameData", function (emittedData) {
    Clinic.findOne({ _id: emittedData.clinicID }, function (err, result) {
      if (result) {
        setInterval(async function () {
          var patientArray = await getPatientsForClinic(emittedData.clinic)
          socket.emit("iFrameData", patientArray)
        }, 10000)
      } else {
        socket.emit("iFrameData", "Error: Not Authenticated")
      }
    })
  })
})

httpServer.listen(process.env.PORT || 8081, () => {
  console.log(`Started on ${process.env.PORT || 8081}`)
})
