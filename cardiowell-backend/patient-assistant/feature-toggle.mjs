import Patient from "../models/patient.js"
import Clinic from "../models/clinic.js"
import {
  clinicScope,
  findTogglesByScopes,
  patientScope,
} from "../feature-toggle/feature-toggle.collection.mjs"

const findClinicByPatient = async patient => {
  // @todo: add clinicId to patient model to avoid this lookup
  const clinicName = patient.clinic
  return await Clinic.findOne({ name: clinicName })
}

/**
 * @param {typeof Patient.prototype} patient
 * @returns {Promise<boolean>}
 */
export const isBPBuddyEnabled = async patient => {
  const enabledForAppInstance = process.env.FEATURE_BP_BUDDY_ENABLED === "true"
  if (!enabledForAppInstance) {
    return false
  }

  const clinic = await findClinicByPatient(patient)
  if (!clinic) {
    return false
  }

  const [clinicToggle, patientToggle] = await findTogglesByScopes("BPBuddy", [
    clinicScope(clinic._id),
    patientScope(patient._id),
  ])

  if (patientToggle && patientToggle.value !== "inherit") {
    return patientToggle.value === "enabled"
  }

  if (!clinicToggle) {
    return false
  }

  return clinicToggle.value !== "disabled"
}
